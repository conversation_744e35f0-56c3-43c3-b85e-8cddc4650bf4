<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS-Only Gradient Test</title>
    <style>
        body {
            margin: 0;
            padding: 40px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-box {
            width: 400px;
            height: 60px;
            margin: 20px auto;
            border-radius: 30px;
            position: relative;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #333;
        }
        
        /* Method 1: Background-based gradient */
        .gradient-box.method1 {
            background: linear-gradient(white, white) padding-box, 
                        conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            border: 3px solid transparent;
            animation: rotateGradientMethod1 3s linear infinite;
        }
        
        @keyframes rotateGradientMethod1 {
            0% {
                background: linear-gradient(white, white) padding-box, 
                            conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
            25% {
                background: linear-gradient(white, white) padding-box, 
                            conic-gradient(from 90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
            50% {
                background: linear-gradient(white, white) padding-box, 
                            conic-gradient(from 180deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
            75% {
                background: linear-gradient(white, white) padding-box, 
                            conic-gradient(from 270deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
            100% {
                background: linear-gradient(white, white) padding-box, 
                            conic-gradient(from 360deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
        }
        
        /* Method 2: Pseudo-element with rotation */
        .gradient-box.method2 {
            border: 3px solid transparent;
            background: white;
        }
        
        .gradient-box.method2::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b);
            border-radius: 33px;
            z-index: -1;
            animation: spinGradient 3s linear infinite;
        }
        
        @keyframes spinGradient {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        /* Method 3: Simple border animation */
        .gradient-box.method3 {
            border: 3px solid;
            border-image: conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) 1;
            animation: borderRotate 3s linear infinite;
        }
        
        @keyframes borderRotate {
            0% {
                border-image: conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) 1;
            }
            25% {
                border-image: conic-gradient(from 90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) 1;
            }
            50% {
                border-image: conic-gradient(from 180deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) 1;
            }
            75% {
                border-image: conic-gradient(from 270deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) 1;
            }
            100% {
                border-image: conic-gradient(from 360deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) 1;
            }
        }
        
        .section {
            margin: 40px 0;
            text-align: center;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .section p {
            color: #666;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center;">🌈 CSS-Only Gradient Border Tests</h1>
        <p style="text-align: center; color: #666;">Testing different methods for rotating gradient borders</p>
        
        <div class="section">
            <h3>Method 1: Background-based Gradient</h3>
            <p>Uses background with padding-box and border-box</p>
            <div class="gradient-box method1">
                Background Method
            </div>
        </div>
        
        <div class="section">
            <h3>Method 2: Pseudo-element with Rotation</h3>
            <p>Uses ::before pseudo-element with transform rotation</p>
            <div class="gradient-box method2">
                Pseudo-element Method
            </div>
        </div>
        
        <div class="section">
            <h3>Method 3: Border-image Animation</h3>
            <p>Uses border-image with conic-gradient</p>
            <div class="gradient-box method3">
                Border-image Method
            </div>
        </div>
        
        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
            <h3 style="color: #28a745; margin-top: 0;">✅ Success Criteria</h3>
            <p style="color: #666; margin-bottom: 0;">You should see 3 boxes above, each with a different rotating rainbow gradient border animation.</p>
        </div>
    </div>
</body>
</html>
