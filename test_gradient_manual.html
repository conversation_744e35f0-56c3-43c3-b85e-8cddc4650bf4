<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Gradient Test</title>
    <link rel="stylesheet" href="static/css/chatgpt-style-clean.css">
    <style>
        body {
            margin: 0;
            padding: 40px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .test-buttons {
            margin: 20px 0;
            display: flex;
            gap: 10px;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .test-btn.show {
            background: #4CAF50;
            color: white;
        }
        
        .test-btn.hide {
            background: #f44336;
            color: white;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 5px;
            font-family: monospace;
        }
        
        .chat-input-container {
            position: relative !important;
            top: auto !important;
            left: auto !important;
            transform: none !important;
            max-width: 600px !important;
            margin: 20px auto !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Manual Gradient Border Test</h1>
        
        <div class="test-buttons">
            <button class="test-btn show" onclick="showGradient()">Show Gradient Border</button>
            <button class="test-btn hide" onclick="hideGradient()">Hide Gradient Border</button>
            <button class="test-btn" onclick="toggleGradient()" style="background: #2196F3; color: white;">Toggle Gradient</button>
        </div>
        
        <div class="status" id="status">
            Status: Ready for testing
        </div>
        
        <!-- Replicate the exact structure from the main app -->
        <footer class="chat-input-container">
            <form id="chatForm" class="chat-input-form">
                <div class="chatgpt-input-wrapper" id="inputWrapper">
                    <!-- Main textarea at top -->
                    <div class="chatgpt-input-area">
                        <textarea id="userInput" placeholder="Ask anything" rows="1" autocomplete="off"></textarea>
                    </div>

                    <!-- Bottom tools section -->
                    <div class="chatgpt-bottom-tools">
                        <div class="chatgpt-left-tools">
                            <button type="button" class="chatgpt-tool-btn" title="Upload Documents">
                                <span>📎</span>
                            </button>
                            <button type="button" class="chatgpt-tool-btn" title="Escalate to HR">
                                <span>⚠️</span>
                            </button>
                        </div>

                        <div class="chatgpt-right-tools">
                            <!-- Voice input -->
                            <button type="button" class="chatgpt-tool-btn" title="Voice Input">
                                <span>🎤</span>
                            </button>
                            <!-- Send button -->
                            <button type="submit" id="sendBtn" class="chatgpt-send-btn" title="Send message" disabled>
                                <span>➤</span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </footer>
    </div>

    <script>
        const inputWrapper = document.getElementById('inputWrapper');
        const userInput = document.getElementById('userInput');
        const status = document.getElementById('status');

        function updateStatus(message) {
            status.textContent = `Status: ${message}`;
            console.log(message);
        }

        function showGradient() {
            inputWrapper.classList.add('has-text');
            updateStatus('Added has-text class - gradient should be visible');
            console.log('Current classes:', inputWrapper.className);
        }

        function hideGradient() {
            inputWrapper.classList.remove('has-text');
            updateStatus('Removed has-text class - gradient should be hidden');
            console.log('Current classes:', inputWrapper.className);
        }

        function toggleGradient() {
            if (inputWrapper.classList.contains('has-text')) {
                hideGradient();
            } else {
                showGradient();
            }
        }

        // Test automatic detection
        userInput.addEventListener('input', function() {
            const hasContent = this.value.trim() !== '';
            updateStatus(`Input changed, hasContent: ${hasContent}`);
            
            if (hasContent) {
                showGradient();
            } else {
                hideGradient();
            }
        });

        updateStatus('Manual test ready - use buttons or type in input');
    </script>
</body>
</html>
