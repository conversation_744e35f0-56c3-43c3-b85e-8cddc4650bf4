#!/usr/bin/env python3
"""
Simple Flask server to test the rotating glow animation
without the complex dependencies of the main app.
"""

from flask import Flask, send_from_directory, render_template_string
import os

app = Flask(__name__)

@app.route('/')
def index():
    return send_from_directory('.', 'test_rotating_glow.html')

@app.route('/test')
def test():
    return send_from_directory('.', 'test_rotating_glow.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory('static', filename)

if __name__ == '__main__':
    print("🌟 Starting simple server to test rotating glow animation...")
    print("📍 Open http://localhost:5000 in your browser")
    print("📍 Or http://localhost:5000/test for the test page")
    app.run(debug=True, port=5000)
