<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Gradient Border</title>
    <style>
        body {
            margin: 0;
            padding: 40px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .debug-info {
            background: #f0f8ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .console-commands {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
        }
        
        .console-commands h3 {
            color: #63b3ed;
            margin-top: 0;
        }
        
        .command {
            background: #4a5568;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            cursor: pointer;
        }
        
        .command:hover {
            background: #2d3748;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔥 Gradient Border Debug Helper</h1>
        
        <div class="debug-info">
            <h3>Debug Instructions:</h3>
            <ol>
                <li><strong>Open Browser Console</strong> (F12 → Console tab)</li>
                <li><strong>Go to your main application</strong> in another tab</li>
                <li><strong>Type in the message input box</strong> and watch console messages</li>
                <li><strong>Use the commands below</strong> to manually test the gradient border</li>
            </ol>
        </div>
        
        <div class="console-commands">
            <h3>Console Commands to Test:</h3>
            
            <div class="command" onclick="copyToClipboard(this.textContent)">
// Check if elements exist
console.log('userInput:', document.getElementById('userInput'));
console.log('inputWrapper:', document.querySelector('.chatgpt-input-wrapper'));
            </div>
            
            <div class="command" onclick="copyToClipboard(this.textContent)">
// Manually show gradient border
if (typeof updateGradientBorder === 'function') {
    updateGradientBorder(true);
} else {
    console.error('updateGradientBorder function not found');
}
            </div>
            
            <div class="command" onclick="copyToClipboard(this.textContent)">
// Manually hide gradient border
if (typeof updateGradientBorder === 'function') {
    updateGradientBorder(false);
} else {
    console.error('updateGradientBorder function not found');
}
            </div>
            
            <div class="command" onclick="copyToClipboard(this.textContent)">
// Check current classes on input wrapper
const wrapper = document.querySelector('.chatgpt-input-wrapper');
if (wrapper) {
    console.log('Current classes:', wrapper.className);
    console.log('Has has-text class:', wrapper.classList.contains('has-text'));
} else {
    console.error('Input wrapper not found');
}
            </div>
            
            <div class="command" onclick="copyToClipboard(this.textContent)">
// Force add has-text class
const wrapper = document.querySelector('.chatgpt-input-wrapper');
if (wrapper) {
    wrapper.classList.add('has-text');
    wrapper.style.border = '3px solid red';
    wrapper.style.boxShadow = '0 0 20px rgba(255, 107, 107, 0.8)';
    console.log('Forced gradient border ON');
} else {
    console.error('Input wrapper not found');
}
            </div>
            
            <div class="command" onclick="copyToClipboard(this.textContent)">
// Force remove has-text class
const wrapper = document.querySelector('.chatgpt-input-wrapper');
if (wrapper) {
    wrapper.classList.remove('has-text');
    wrapper.style.border = '';
    wrapper.style.boxShadow = '';
    console.log('Forced gradient border OFF');
} else {
    console.error('Input wrapper not found');
}
            </div>
            
            <div class="command" onclick="copyToClipboard(this.textContent)">
// Simulate typing in input
const input = document.getElementById('userInput');
if (input) {
    input.value = 'test message';
    input.dispatchEvent(new Event('input', { bubbles: true }));
    console.log('Simulated typing in input');
} else {
    console.error('User input not found');
}
            </div>
        </div>
        
        <div class="debug-info">
            <h3>What to Look For:</h3>
            <ul>
                <li><strong>🔥 GRADIENT DEBUG</strong> messages in console</li>
                <li><strong>Red border and shadow</strong> appearing around input box</li>
                <li><strong>has-text class</strong> being added/removed</li>
                <li><strong>Error messages</strong> if elements aren't found</li>
            </ul>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
            <h3 style="color: #856404; margin-top: 0;">💡 Pro Tip:</h3>
            <p style="color: #856404; margin-bottom: 0;">Click any command above to copy it to clipboard, then paste in browser console!</p>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text.trim()).then(function() {
                console.log('Command copied to clipboard:', text.trim());
                alert('Command copied to clipboard! Paste it in the browser console.');
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('Could not copy to clipboard. Please copy manually.');
            });
        }
    </script>
</body>
</html>
