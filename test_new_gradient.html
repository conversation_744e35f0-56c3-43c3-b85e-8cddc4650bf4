<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Gradient Border Test</title>
    <link rel="stylesheet" href="static/css/chatgpt-style-clean.css">
    <style>
        body {
            margin: 0;
            padding: 40px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .test-buttons {
            margin: 20px 0;
            display: flex;
            gap: 10px;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            color: white;
        }
        
        .show { background: #4CAF50; }
        .hide { background: #f44336; }
        .toggle { background: #2196F3; }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 5px;
            font-family: monospace;
        }
        
        .chat-input-container {
            position: relative !important;
            top: auto !important;
            left: auto !important;
            transform: none !important;
            max-width: 600px !important;
            margin: 20px auto !important;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 10px;
        }
        
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌈 New Gradient Border Animation Test</h1>
        
        <div class="demo-section">
            <h3>Manual Control Test</h3>
            <div class="test-buttons">
                <button class="test-btn show" onclick="showGradient()">Show Gradient</button>
                <button class="test-btn hide" onclick="hideGradient()">Hide Gradient</button>
                <button class="test-btn toggle" onclick="toggleGradient()">Toggle Gradient</button>
            </div>
            
            <div class="status" id="status">
                Status: Ready for testing
            </div>
        </div>
        
        <div class="demo-section">
            <h3>Automatic Detection Test</h3>
            <p>Type in the input box below to automatically trigger the gradient border:</p>
            
            <!-- Exact structure from main app -->
            <footer class="chat-input-container">
                <form id="chatForm" class="chat-input-form">
                    <div class="chatgpt-input-wrapper" id="inputWrapper">
                        <!-- Main textarea at top -->
                        <div class="chatgpt-input-area">
                            <textarea id="userInput" placeholder="Type here to test gradient border..." rows="1" autocomplete="off"></textarea>
                        </div>

                        <!-- Bottom tools section -->
                        <div class="chatgpt-bottom-tools">
                            <div class="chatgpt-left-tools">
                                <button type="button" class="chatgpt-tool-btn" title="Upload Documents">
                                    <span>📎</span>
                                </button>
                            </div>

                            <div class="chatgpt-right-tools">
                                <button type="button" class="chatgpt-tool-btn" title="Voice Input">
                                    <span>🎤</span>
                                </button>
                                <button type="submit" id="sendBtn" class="chatgpt-send-btn" title="Send message" disabled>
                                    <span>➤</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </footer>
        </div>
        
        <div class="demo-section">
            <h3>Console Commands</h3>
            <p>Open browser console (F12) and try these commands:</p>
            <ul>
                <li><code>showGradientBorder()</code> - Show gradient border</li>
                <li><code>hideGradientBorder()</code> - Hide gradient border</li>
                <li><code>toggleGradientBorder()</code> - Toggle gradient border</li>
            </ul>
        </div>
    </div>

    <!-- Include the new gradient border script -->
    <script src="static/js/gradient-border-animation.js"></script>
    
    <script>
        const inputWrapper = document.getElementById('inputWrapper');
        const userInput = document.getElementById('userInput');
        const status = document.getElementById('status');

        function updateStatus(message) {
            status.textContent = `Status: ${message}`;
            console.log('📝 Status:', message);
        }

        function showGradient() {
            if (window.showGradientBorder) {
                window.showGradientBorder();
                updateStatus('Manual: Gradient border shown');
            } else {
                inputWrapper.classList.add('has-text');
                updateStatus('Manual: Added has-text class directly');
            }
        }

        function hideGradient() {
            if (window.hideGradientBorder) {
                window.hideGradientBorder();
                updateStatus('Manual: Gradient border hidden');
            } else {
                inputWrapper.classList.remove('has-text');
                updateStatus('Manual: Removed has-text class directly');
            }
        }

        function toggleGradient() {
            if (window.toggleGradientBorder) {
                window.toggleGradientBorder();
                updateStatus('Manual: Toggled gradient border');
            } else {
                if (inputWrapper.classList.contains('has-text')) {
                    hideGradient();
                } else {
                    showGradient();
                }
            }
        }

        // Monitor input changes
        userInput.addEventListener('input', function() {
            const hasContent = this.value.trim() !== '';
            updateStatus(`Input changed: "${this.value}" (hasContent: ${hasContent})`);
        });

        updateStatus('Test page loaded - ready for testing');
    </script>
</body>
</html>
