#!/usr/bin/env python3
"""
Simple Flask server to test the pre-login rotating glow animation
with the actual CSS files.
"""

from flask import Flask, send_from_directory, render_template
import os

app = Flask(__name__, template_folder='templates', static_folder='static')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory('static', filename)

if __name__ == '__main__':
    print("🌟 Starting pre-login test server...")
    print("📍 Open http://localhost:5001 in your browser")
    print("📍 This will load the actual pre-login UI with all CSS files")
    app.run(debug=True, port=5001)
