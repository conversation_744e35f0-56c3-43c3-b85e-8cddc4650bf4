<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rotating Glow Animation Test</title>
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 40px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }

        .test-section h3 {
            margin-top: 0;
            color: #555;
        }

        /* ===== ROTATING GLOW ANIMATION FOR MESSAGE INPUT BOX ===== */
        /* Clean implementation that only shows when text is present */

        /* Pre-login container styles */
        .pre-login-bottom-input-container {
            margin-bottom: 20px;
        }

        .pre-login-chat-input-form {
            width: 100%;
        }

        /* Ensure input wrapper has proper positioning */
        .chatgpt-input-wrapper {
            position: relative !important;
            overflow: visible !important;
            display: flex !important;
            flex-direction: column !important;
            background: #ffffff !important;
            border: 1px solid #d1d5db !important;
            border-radius: 26px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            padding: 16px !important;
            gap: 12px !important;
            min-height: 80px !important;
            transition: border-color 0.15s ease, box-shadow 0.15s ease !important;
        }

        /* Rotating glow animation - only when has-text class is present */
        .chatgpt-input-wrapper.has-text::before {
            content: '' !important;
            position: absolute !important;
            top: -3px !important;
            left: -3px !important;
            right: -3px !important;
            bottom: -3px !important;
            background: conic-gradient(
                from 0deg,
                #ff6b6b,
                #4ecdc4,
                #45b7d1,
                #96ceb4,
                #feca57,
                #ff9ff3,
                #54a0ff,
                #5f27cd,
                #ff6b6b
            ) !important;
            border-radius: 30px !important;
            z-index: -1 !important;
            animation: rotateGlow 3s linear infinite !important;
            pointer-events: none !important;
        }

        /* Add subtle glow effect when text is present */
        .chatgpt-input-wrapper.has-text {
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.3) !important;
        }

        /* Keyframe animation for rotating the gradient */
        @keyframes rotateGlow {
            0% {
                transform: rotate(0deg) !important;
            }
            100% {
                transform: rotate(360deg) !important;
            }
        }

        /* Input area styling */
        .chatgpt-input-area {
            flex: 1;
        }

        .chatgpt-input-area textarea {
            width: 100%;
            border: none;
            outline: none;
            background: transparent;
            resize: none;
            font-size: 1.08rem;
            padding: 0;
            min-height: 24px;
            max-height: 120px;
            color: #222;
            font-family: inherit;
            line-height: 1.5;
        }

        .chatgpt-input-area textarea::placeholder {
            color: #b0b0b0;
            font-size: 1.08rem;
        }

        /* Tools area */
        .chatgpt-tools {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chatgpt-left-tools, .chatgpt-right-tools {
            display: flex;
            gap: 8px;
        }

        .chatgpt-tool-btn {
            background: none;
            border: none;
            color: #666;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
        }

        .chatgpt-tool-btn:hover {
            background: #f0f0f0;
            color: #333;
        }

        .chatgpt-send-btn {
            background: #ececec;
            border: none;
            color: #b0b0b0;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s, color 0.2s;
            cursor: pointer;
        }

        .chatgpt-send-btn:enabled {
            background: #19c37d;
            color: #fff;
        }

        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .instructions h4 {
            margin-top: 0;
            color: #333;
        }

        .instructions ul {
            margin-bottom: 0;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .manual-controls {
            margin-top: 20px;
            text-align: center;
        }

        .manual-controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 14px;
        }

        .manual-controls button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Rotating Glow Animation Test</h1>
        
        <div class="instructions">
            <h4>Instructions:</h4>
            <ul>
                <li><strong>Type in the input box below</strong> - The rotating glow animation should appear around the border</li>
                <li><strong>Clear the text</strong> - The animation should disappear</li>
                <li><strong>Use manual controls</strong> - Test the animation manually with the buttons below</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Pre-Login Message Input Box with Rotating Glow Animation</h3>

            <div class="pre-login-bottom-input-container">
                <form class="pre-login-chat-input-form">
                    <div class="chatgpt-input-wrapper" id="preLoginInputWrapper">
                        <!-- Main textarea at top -->
                        <div class="chatgpt-input-area">
                            <textarea class="pre-login-input" id="preLoginInput" placeholder="How can I help you today?" rows="1"></textarea>
                        </div>

                        <!-- Tools at bottom -->
                        <div class="chatgpt-tools">
                            <div class="chatgpt-left-tools">
                                <button type="button" class="chatgpt-tool-btn" title="Attach File">
                                    📎
                                </button>
                                <button type="button" class="chatgpt-tool-btn" title="Voice Input">
                                    🎤
                                </button>
                            </div>

                            <div class="chatgpt-right-tools">
                                <button type="button" id="preLoginSendBtn" class="chatgpt-send-btn" title="Send message" disabled>
                                    ↑
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="manual-controls">
                <button onclick="showPreLoginAnimation()">Show Pre-Login Animation</button>
                <button onclick="hidePreLoginAnimation()">Hide Pre-Login Animation</button>
            </div>
        </div>

        <div class="test-section">
            <h3>Logged-In Message Input Box with Rotating Glow Animation</h3>
            
            <div class="chatgpt-input-wrapper" id="testInputWrapper">
                <!-- Main textarea at top -->
                <div class="chatgpt-input-area">
                    <textarea id="testInput" placeholder="Type your message here..." rows="1"></textarea>
                </div>
                
                <!-- Tools at bottom -->
                <div class="chatgpt-tools">
                    <div class="chatgpt-left-tools">
                        <button type="button" class="chatgpt-tool-btn" title="Attach File">
                            📎
                        </button>
                        <button type="button" class="chatgpt-tool-btn" title="Voice Input">
                            🎤
                        </button>
                    </div>
                    
                    <div class="chatgpt-right-tools">
                        <button type="button" id="testSendBtn" class="chatgpt-send-btn" title="Send message" disabled>
                            ↑
                        </button>
                    </div>
                </div>
            </div>

            <div class="manual-controls">
                <button onclick="showAnimation()">Show Animation</button>
                <button onclick="hideAnimation()">Hide Animation</button>
                <button onclick="toggleAnimation()">Toggle Animation</button>
            </div>
        </div>
    </div>

    <script>
        // Get elements for logged-in UI
        const testInput = document.getElementById('testInput');
        const testSendBtn = document.getElementById('testSendBtn');
        const inputWrapper = document.getElementById('testInputWrapper');

        // Get elements for pre-login UI
        const preLoginInput = document.getElementById('preLoginInput');
        const preLoginSendBtn = document.getElementById('preLoginSendBtn');
        const preLoginInputWrapper = document.getElementById('preLoginInputWrapper');

        // Function to update the rotating glow animation
        function updateGradientBorder(hasContent = null) {
            if (hasContent === null) {
                hasContent = testInput.value.trim() !== '';
            }

            if (hasContent) {
                inputWrapper.classList.add('has-text');
                testSendBtn.disabled = false;
                console.log('✨ Rotating glow animation enabled');
            } else {
                inputWrapper.classList.remove('has-text');
                testSendBtn.disabled = true;
                console.log('✨ Rotating glow animation disabled');
            }
        }

        // Add input event listeners
        testInput.addEventListener('input', function() {
            updateGradientBorder();
        });

        preLoginInput.addEventListener('input', function() {
            updatePreLoginGradientBorder();
        });

        // Function to update the pre-login rotating glow animation
        function updatePreLoginGradientBorder(hasContent = null) {
            if (hasContent === null) {
                hasContent = preLoginInput.value.trim() !== '';
            }

            if (hasContent) {
                preLoginInputWrapper.classList.add('has-text');
                preLoginSendBtn.disabled = false;
                console.log('✨ Pre-login rotating glow animation enabled');
            } else {
                preLoginInputWrapper.classList.remove('has-text');
                preLoginSendBtn.disabled = true;
                console.log('✨ Pre-login rotating glow animation disabled');
            }
        }

        // Manual control functions for logged-in UI
        function showAnimation() {
            updateGradientBorder(true);
        }

        function hideAnimation() {
            updateGradientBorder(false);
        }

        function toggleAnimation() {
            const hasAnimation = inputWrapper.classList.contains('has-text');
            updateGradientBorder(!hasAnimation);
        }

        // Manual control functions for pre-login UI
        function showPreLoginAnimation() {
            updatePreLoginGradientBorder(true);
        }

        function hidePreLoginAnimation() {
            updatePreLoginGradientBorder(false);
        }

        // Initialize both inputs
        updateGradientBorder(false);
        updatePreLoginGradientBorder(false);

        console.log('🌟 Rotating glow animation test page loaded!');
        console.log('Type in either input box to see the animation in action.');
    </script>
</body>
</html>
