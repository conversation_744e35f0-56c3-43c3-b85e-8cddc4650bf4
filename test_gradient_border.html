<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Gradient Border Animation</title>
    <link rel="stylesheet" href="static/css/chatgpt-style-clean.css">
    <style>
        body {
            margin: 0;
            padding: 40px;
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .test-container {
            width: 100%;
            max-width: 600px;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .instructions {
            background: #f0f8ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .instructions ul {
            margin-bottom: 0;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Gradient Border Animation Test</h1>
        
        <div class="instructions">
            <h3>How to Test:</h3>
            <ul>
                <li><strong>Start typing</strong> in the input box below</li>
                <li><strong>Watch</strong> as the animated gradient border appears around the input</li>
                <li><strong>Clear the text</strong> and see the gradient border disappear</li>
                <li><strong>Try different text lengths</strong> to see the animation in action</li>
            </ul>
        </div>
        
        <!-- Test input container -->
        <div class="chat-input-container" style="position: relative; top: auto; left: auto; transform: none; max-width: 100%; padding: 20px 0;">
            <form class="chat-input-form">
                <div class="chatgpt-input-wrapper">
                    <!-- Main textarea at top -->
                    <div class="chatgpt-input-area">
                        <textarea id="testInput" placeholder="Start typing to see the gradient border animation..." rows="1" autocomplete="off"></textarea>
                    </div>

                    <!-- Bottom tools section -->
                    <div class="chatgpt-bottom-tools">
                        <div class="chatgpt-left-tools">
                            <button type="button" class="chatgpt-tool-btn" title="Upload Documents">
                                <span>📎</span>
                            </button>
                            <button type="button" class="chatgpt-tool-btn" title="Escalate to HR">
                                <span>⚠️</span>
                            </button>
                        </div>
                        <div class="chatgpt-right-tools">
                            <button type="button" class="chatgpt-tool-btn" title="Voice Input">
                                <span>🎤</span>
                            </button>
                            <button type="submit" id="testSendBtn" class="send-btn-chatgpt" disabled>
                                <span>➤</span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Test script to handle the gradient border animation
        const testInput = document.getElementById('testInput');
        const testSendBtn = document.getElementById('testSendBtn');
        const inputWrapper = document.querySelector('.chatgpt-input-wrapper');

        function updateGradientBorder(hasContent = null) {
            if (hasContent === null) {
                hasContent = testInput.value.trim() !== '';
            }

            if (hasContent) {
                inputWrapper.classList.add('has-text');
            } else {
                inputWrapper.classList.remove('has-text');
            }
        }

        // Handle input changes
        testInput.addEventListener('input', function() {
            const hasContent = this.value.trim() !== '';

            // Debug logging
            console.log('Input changed, hasContent:', hasContent);
            console.log('Input wrapper classes:', inputWrapper.className);

            // Update send button state
            testSendBtn.disabled = !hasContent;

            // Update gradient border
            updateGradientBorder(hasContent);

            // Auto-resize
            this.style.height = 'auto';
            const newHeight = Math.min(this.scrollHeight, 200);
            this.style.height = newHeight + 'px';
        });

        // Handle form submission
        document.querySelector('.chat-input-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (testInput.value.trim()) {
                alert('Message sent: ' + testInput.value);
                
                // Clear input and remove gradient
                testInput.value = '';
                testInput.style.height = 'auto';
                testSendBtn.disabled = true;
                updateGradientBorder(false);
            }
        });

        // Initialize
        updateGradientBorder(false);
    </script>
</body>
</html>
