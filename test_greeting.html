<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Greeting Message</title>
    <link rel="stylesheet" href="static/css/chatgpt-style-clean.css">
    <style>
        :root {
            --bg-primary: #FFFFFF;
            --text-primary: #000000;
            --text-secondary: #666666;
            --border-color: #E5E5E5;
            --accent-color: #333333;
            --accent-hover: #555555;
            --button-bg: #000000;
            --button-text: #FFFFFF;
            --button-hover-bg: #333333;
            --input-bg: #FFFFFF;
            --input-border: #E5E5E5;
            --input-text: #000000;
            --input-placeholder: #999999;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background-color: var(--bg-primary);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            width: 100%;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Dynamic greeting message - positioned in the middle of the page -->
        <div class="greeting-message-container" id="greetingMessageContainer" style="display: block;">
            <div class="welcome-message">
                <h2 id="welcomeTitleDynamic" class="greeting-message">👋 Hi VijayKodam, 🌆 Good evening!</h2>
                <p>I can help you with questions about company policies, employee guidelines, and HR procedures.</p>
                <div class="suggestion-chips">
                    <button class="suggestion-chip" onclick="alert('Leave Policy clicked')">🗓️ Leave Policy</button>
                    <button class="suggestion-chip" onclick="alert('Referral Program clicked')">👥 Referral Program</button>
                    <button class="suggestion-chip" onclick="alert('Dress Code clicked')">👔 Dress Code</button>
                    <button class="suggestion-chip" onclick="alert('Work from Home clicked')">🏠 Work from Home</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
