<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Gradient Border Test</title>
    <style>
        body {
            margin: 0;
            padding: 40px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .test-container {
            width: 600px;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .input-wrapper {
            position: relative;
            background: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 26px;
            padding: 16px;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            overflow: hidden;
        }
        
        /* Animated gradient border that moves around the input box */
        .input-wrapper {
            position: relative;
            overflow: visible;
        }

        /* Create the animated gradient border using ::after pseudo-element */
        .input-wrapper::after {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b);
            border-radius: 30px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
            animation: rotateGradient 3s linear infinite;
            pointer-events: none;
        }

        /* Show the animated gradient border when input has text */
        .input-wrapper.has-text::after {
            opacity: 1;
        }

        /* Alternative method using border-image for better browser support */
        .input-wrapper.has-text {
            border: 3px solid transparent;
            background: linear-gradient(white, white) padding-box,
                        conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            animation: rotateGradientBorder 3s linear infinite;
        }
        
        .input-area {
            width: 100%;
        }
        
        .input-area textarea {
            width: 100%;
            border: none;
            outline: none;
            background: transparent;
            resize: none;
            font-size: 16px;
            padding: 14px 0;
            min-height: 24px;
            max-height: 120px;
            color: #222;
            font-family: inherit;
            line-height: 1.5;
        }
        
        .input-area textarea::placeholder {
            color: #b0b0b0;
            font-size: 16px;
        }
        
        .bottom-tools {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 8px;
        }
        
        .left-tools {
            display: flex;
            gap: 8px;
        }
        
        .tool-btn {
            background: #f5f5f5;
            border: none;
            color: #666;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .tool-btn:hover {
            background: #e5e5e5;
        }
        
        .send-btn {
            background: #ececec;
            border: none;
            color: #b0b0b0;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s, color 0.2s;
            cursor: pointer;
        }
        
        .send-btn:enabled {
            background: #19c37d;
            color: #fff;
        }
        
        /* Keyframes for rotating gradient animation */
        @keyframes rotateGradient {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes rotateGradientBorder {
            0% {
                background: linear-gradient(white, white) padding-box,
                            conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
            25% {
                background: linear-gradient(white, white) padding-box,
                            conic-gradient(from 90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
            50% {
                background: linear-gradient(white, white) padding-box,
                            conic-gradient(from 180deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
            75% {
                background: linear-gradient(white, white) padding-box,
                            conic-gradient(from 270deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
            100% {
                background: linear-gradient(white, white) padding-box,
                            conic-gradient(from 360deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
            }
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Simple Gradient Border Test</h1>
        
        <div class="instructions">
            <h3>Test Instructions:</h3>
            <ul>
                <li><strong>Type in the input box</strong> - gradient border should appear</li>
                <li><strong>Clear the text</strong> - gradient border should disappear</li>
                <li><strong>Check console</strong> for debug messages</li>
            </ul>
        </div>
        
        <div class="input-wrapper" id="inputWrapper">
            <div class="input-area">
                <textarea id="testInput" placeholder="Start typing to see the gradient border..." rows="1"></textarea>
            </div>
            <div class="bottom-tools">
                <div class="left-tools">
                    <button class="tool-btn">📎</button>
                    <button class="tool-btn">⚠️</button>
                </div>
                <div class="right-tools">
                    <button class="tool-btn">🎤</button>
                    <button class="send-btn" id="sendBtn" disabled>➤</button>
                </div>
            </div>
        </div>
        
        <div class="status" id="status">
            Status: Ready to test
        </div>
    </div>

    <script>
        const testInput = document.getElementById('testInput');
        const sendBtn = document.getElementById('sendBtn');
        const inputWrapper = document.getElementById('inputWrapper');
        const status = document.getElementById('status');

        function updateStatus(message) {
            status.textContent = `Status: ${message}`;
            console.log(message);
        }

        function updateGradientBorder(hasContent) {
            updateStatus(`updateGradientBorder called with hasContent: ${hasContent}`);
            
            if (hasContent) {
                inputWrapper.classList.add('has-text');
                updateStatus('Added has-text class - gradient should be visible');
            } else {
                inputWrapper.classList.remove('has-text');
                updateStatus('Removed has-text class - gradient should be hidden');
            }
            
            console.log('Input wrapper classes:', inputWrapper.className);
        }

        // Handle input changes
        testInput.addEventListener('input', function() {
            const hasContent = this.value.trim() !== '';
            
            updateStatus(`Input changed, text length: ${this.value.length}, hasContent: ${hasContent}`);
            
            // Update send button state
            sendBtn.disabled = !hasContent;
            
            // Update gradient border
            updateGradientBorder(hasContent);
            
            // Auto-resize
            this.style.height = 'auto';
            const newHeight = Math.min(this.scrollHeight, 200);
            this.style.height = newHeight + 'px';
        });

        // Initialize
        updateGradientBorder(false);
        updateStatus('Initialized - ready for testing');
    </script>
</body>
</html>
